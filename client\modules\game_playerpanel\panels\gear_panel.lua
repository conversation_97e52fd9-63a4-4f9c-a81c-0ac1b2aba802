-- gear_panel.lua
-- Handles the gear panel content and functionality

local GearPanel = {}

local gearSlots = {}

-- Equipment slot configuration table
local slotConfigs = {
  [1] = { -- CONST_SLOT_HEAD
    name = "Head",
    label = "Head",
    slotId = 1,
    emptyIcon = "#8B4513" -- Brown color for helmet
  },
  [2] = { -- CONST_SLOT_NECKLACE
    name = "Necklace",
    label = "Neck",
    slotId = 2,
    emptyIcon = "#FFD700" -- Gold color for necklace
  },
  [3] = { -- CONST_SLOT_BACKPACK
    name = "Backpack",
    label = "Back",
    slotId = 3,
    emptyIcon = "#654321" -- Dark brown for backpack
  },
  [4] = { -- CONST_SLOT_ARMOR
    name = "Armor",
    label = "Armor",
    slotId = 4,
    emptyIcon = "#C0C0C0" -- Silver for armor
  },
  [5] = { -- CONST_SLOT_HAND
    name = "Hand",
    label = "Weapon",
    slotId = 5,
    emptyIcon = "#8B0000" -- Dark red for weapon
  },
  [6] = { -- CONST_SLOT_LEGS
    name = "Legs",
    label = "Legs",
    slotId = 6,
    emptyIcon = "#4169E1" -- Royal blue for legs
  },
  [7] = { -- CONST_SLOT_FEET
    name = "Feet",
    label = "Feet",
    slotId = 7,
    emptyIcon = "#8B4513" -- Brown for boots
  },
  [8] = { -- CONST_SLOT_RING
    name = "Ring",
    label = "Ring",
    slotId = 8,
    emptyIcon = "#FFD700" -- Gold for ring
  },
  [9] = { -- CONST_SLOT_THROWING
    name = "Throwing",
    label = "Ammo",
    slotId = 9,
    emptyIcon = "#696969" -- Dim gray for ammo
  }
}

function GearPanel.show(contentPanel)
  if not contentPanel then
    g_logger.error("GearPanel: contentPanel is nil")
    return
  end

  -- Add padding to the content panel
  contentPanel:setPaddingLeft(6)
  contentPanel:setPaddingRight(6)
  contentPanel:setPaddingTop(6)
  contentPanel:setPaddingBottom(6)

  -- Create grid layout
  local gridLayout = UIGridLayout.create(contentPanel)
  gridLayout:setCellSize({width = 68, height = 68})
  gridLayout:setCellSpacing(4)
  gridLayout:setFlow(true)
  contentPanel:setLayout(gridLayout)

  -- Create gear slots
  gearSlots = {}
  for slotId, config in pairs(slotConfigs) do
    local slot = GearPanel.createGearSlot(slotId, contentPanel, config)
    if slot then
      gearSlots[slotId] = slot
    end
  end

  -- Update all slots with current equipment
  GearPanel.updateAllSlots()
end

function GearPanel.createGearSlot(slotId, contentPanel, config)
  local slot = g_ui.createWidget('GearSlot', contentPanel)
  if not slot then
    g_logger.error("GearPanel: Failed to create GearSlot widget for slot " .. slotId)
    return nil
  end

  -- Set slot label
  local slotLabel = slot:getChildById('slotLabel')
  if slotLabel then
    slotLabel:setText(config.label)
  end

  -- Set empty slot icon color
  local emptyIcon = slot:getChildById('emptySlotIcon')
  if emptyIcon then
    emptyIcon:setBackgroundColor(config.emptyIcon)
  end

  -- Set up the slot item for drag and drop
  local slotItem = slot:getChildById('slotItem')
  if slotItem then
    -- Set the position for inventory slot (format: {x=65535, y=slotId, z=0})
    slotItem.position = {x = 65535, y = slotId, z = 0}
  end

  -- Add drag and drop handlers to the slot itself
  slot.onDrop = function(widget, draggedWidget, mousePos)
    return GearPanel.onSlotDrop(slotId, widget, draggedWidget, mousePos)
  end

  slot.onDragEnter = function(widget, mousePos)
    return GearPanel.onSlotDragEnter(slotId, widget, mousePos)
  end

  slot.onDragLeave = function(widget, droppedWidget, mousePos)
    return GearPanel.onSlotDragLeave(slotId, widget, droppedWidget, mousePos)
  end

  -- Update slot with current item
  GearPanel.updateSlotItem(slotId, slot, config)
  return slot
end

function GearPanel.updateSlotItem(slotId, slot, config)
  if not slot then
    return
  end

  config = config or slotConfigs[slotId]
  if not config then
    g_logger.error("GearPanel: Unknown slot ID: " .. slotId)
    return
  end

  local localPlayer = g_game.getLocalPlayer()
  if not localPlayer then
    g_logger.error("GearPanel: No local player found when updating slot " .. slotId)
    return
  end

  -- Get equipped item for this slot
  local equippedItem = localPlayer:getInventoryItem(slotId)

  local slotItem = slot:getChildById('slotItem')
  local emptyIcon = slot:getChildById('emptySlotIcon')

  if equippedItem and slotItem then
    -- Show the equipped item
    slotItem:setItem(equippedItem)
    slotItem:setVisible(true)
    if emptyIcon then
      emptyIcon:setVisible(false)
    end
  else
    -- Show empty slot
    if slotItem then
      slotItem:clearItem()
      slotItem:setVisible(false)
    end
    if emptyIcon then
      emptyIcon:setVisible(true)
    end
  end
end

function GearPanel.updateAllSlots()
  for slotId, slot in pairs(gearSlots) do
    GearPanel.updateSlotItem(slotId, slot, slotConfigs[slotId])
  end
end

function GearPanel.onInventoryChange(slotId, item, oldItem)
  -- Try different methods to get the slot number
  local slotNum = nil

  -- Method 1: Try direct conversion if it's already a number
  if type(slotId) == "number" then
    slotNum = slotId
  else
    -- Method 2: Try to extract numeric value from userdata
    -- The userdata might have a __tostring or __tonumber metamethod
    local slotStr = tostring(slotId)
    local numMatch = slotStr:match("(%d+)")
    if numMatch then
      slotNum = tonumber(numMatch)
    else
      -- Method 3: Try arithmetic operations to force conversion
      local success, result = pcall(function() return slotId + 0 end)
      if success then
        slotNum = result
      else
        -- Method 4: Try using the userdata directly as table index
        -- Sometimes Lua can convert userdata to numbers automatically
        if slotConfigs[slotId] then
          slotNum = slotId
        end
      end
    end
  end

  -- Debug: Log when inventory changes
  g_logger.info("GearPanel: onInventoryChange called for slot " .. tostring(slotNum) .. " (original: " .. tostring(slotId) .. "), item: " .. tostring(item) .. ", oldItem: " .. tostring(oldItem))

  -- Only update if this is an equipment slot we care about
  if slotNum and slotConfigs[slotNum] and gearSlots[slotNum] then
    g_logger.info("GearPanel: Updating slot " .. tostring(slotNum))
    GearPanel.updateSlotItem(slotNum, gearSlots[slotNum], slotConfigs[slotNum])
  else
    g_logger.info("GearPanel: Slot " .. tostring(slotNum) .. " not found in configs or gearSlots")
  end
end

function GearPanel.hide()
  -- Cleanup when hiding
  gearSlots = {}
end

function GearPanel.onSlotDrop(slotId, widget, draggedWidget, mousePos)
  if not draggedWidget or not draggedWidget.currentDragThing then
    return false
  end

  local item = draggedWidget.currentDragThing
  if not item or not item:isItem() then
    return false
  end

  -- Get the target position for this equipment slot
  local toPos = {x = 65535, y = slotId, z = 0}
  local itemPos = item:getPosition()

  -- Don't move if already in the same position
  if itemPos.x == toPos.x and itemPos.y == toPos.y and itemPos.z == toPos.z then
    return false
  end

  -- Move the item to the equipment slot
  if item:getCount() > 1 then
    modules.game_interface.moveStackableItem(item, toPos)
  else
    g_game.move(item, toPos, 1)
  end

  return true
end

function GearPanel.onSlotDragEnter(slotId, widget, mousePos)
  -- Visual feedback when dragging over a slot
  widget:setBackgroundColor('#404040')
  return true
end

function GearPanel.onSlotDragLeave(slotId, widget, droppedWidget, mousePos)
  -- Reset visual feedback when leaving the slot
  widget:setBackgroundColor('#2a2a2a')
  return true
end

function GearPanel.getSlotConfigs()
  return slotConfigs
end

return GearPanel